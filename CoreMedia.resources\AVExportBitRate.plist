<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AVAssetExportBitRateBasic</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>25000000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>3840</integer>
				<key>maxHeight</key>
				<integer>2160</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>13736346</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1440</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>10485760</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1080</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>9615442</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>7340032</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>6181356</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>4718592</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>540</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>3145728</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>2401319</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>360</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>805306</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>240</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>614737</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>180</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateHEVCBasic</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>15000000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>3840</integer>
				<key>maxHeight</key>
				<integer>2160</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>9825000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1440</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>7500000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1080</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>6000000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>5502000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>4200000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>540</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>3000000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>2290076</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>360</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>750000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>240</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>572519</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>180</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateICPL</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>4400000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>3355443</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>1150000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>880804</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>360</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateICPLHEVC</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>7860000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1440</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>6000000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1920</integer>
				<key>maxHeight</key>
				<integer>1080</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>4400000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>3355443</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>1150000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>880804</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>360</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateiPod</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>1572864</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>734003</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>240</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateAppleTV</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>5242880</integer>
				<key>frameRate</key>
				<integer>24</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>4718592</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>540</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>3145728</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>805306</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>240</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateMessage</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>2200000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>1280</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>1250000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>900000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>640</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>700000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>568</integer>
				<key>maxHeight</key>
				<integer>568</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>700000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>480</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>420000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>352</integer>
				<key>maxHeight</key>
				<integer>352</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>420000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>320</integer>
				<key>maxHeight</key>
				<integer>320</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>128000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>224</integer>
				<key>maxHeight</key>
				<integer>224</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>128000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>192</integer>
				<key>maxHeight</key>
				<integer>192</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateMessageHEVC</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>2882000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>2200000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>1250000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>960</integer>
				<key>maxHeight</key>
				<integer>720</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>900000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>640</integer>
				<key>maxHeight</key>
				<integer>480</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>700000</integer>
				<key>frameRate</key>
				<integer>32</integer>
				<key>maxWidth</key>
				<integer>480</integer>
				<key>maxHeight</key>
				<integer>360</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>420000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>352</integer>
				<key>maxHeight</key>
				<integer>198</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>128000</integer>
				<key>frameRate</key>
				<integer>16</integer>
				<key>maxWidth</key>
				<integer>192</integer>
				<key>maxHeight</key>
				<integer>144</integer>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportBitRateYouTubeHD</key>
	<dict>
		<key>BitRateTiers</key>
		<array>
			<dict>
				<key>bitRate</key>
				<integer>4500000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxHeight</key>
				<integer>1280</integer>
				<key>maxWidth</key>
				<integer>1280</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>2500000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxHeight</key>
				<integer>960</integer>
				<key>maxWidth</key>
				<integer>960</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>1800000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxHeight</key>
				<integer>640</integer>
				<key>maxWidth</key>
				<integer>640</integer>
			</dict>
			<dict>
				<key>bitRate</key>
				<integer>840000</integer>
				<key>frameRate</key>
				<integer>30</integer>
				<key>maxHeight</key>
				<integer>320</integer>
				<key>maxWidth</key>
				<integer>320</integer>
			</dict>
		</array>
	</dict>
</dict>
</plist>
