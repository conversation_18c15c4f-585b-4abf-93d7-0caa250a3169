<?xml version="1.0" encoding="UTF-8"?>
<theme>
<themeName>&#924;&#949;&#947;&#940;&#955;&#951; &#955;&#943;&#963;&#964;&#945; &#945;&#957;&#945;&#960;&#945;&#961;&#945;&#947;&#969;&#947;&#942;&#962; (&#913;&#963;&#960;&#961;&#972;&#956;&#945;&#965;&#961;&#945;)</themeName>
<themeExplanation>&#917;&#954;&#964;&#965;&#960;&#974;&#957;&#949;&#953; &#956;&#953;&#945; &#949;&#954;&#964;&#949;&#964;&#945;&#956;&#941;&#957;&#951; &#955;&#943;&#963;&#964;&#945; &#945;&#957;&#945;&#960;&#945;&#961;&#945;&#947;&#969;&#947;&#942;&#962; &#942; &#946;&#953;&#946;&#955;&#953;&#959;&#952;&#942;&#954;&#951; &#958;&#949;&#954;&#953;&#957;&#974;&#957;&#964;&#945;&#962; &#945;&#960;&#972; &#964;&#951;&#957; &#956;&#960;&#961;&#959;&#963;&#964;&#953;&#957;&#942; &#972;&#968;&#951; &#954;&#945;&#953; &#963;&#965;&#957;&#949;&#967;&#943;&#950;&#959;&#957;&#964;&#945;&#962; &#963;&#964;&#951;&#957; &#960;&#943;&#963;&#969;. &#913;&#965;&#964;&#942; &#949;&#943;&#957;&#945;&#953; &#956;&#953;&#945; &#954;&#945;&#955;&#942; &#949;&#960;&#953;&#955;&#959;&#947;&#942; &#947;&#953;&#945; &#955;&#943;&#963;&#964;&#949;&#962; &#945;&#957;&#945;&#960;&#945;&#961;&#945;&#947;&#969;&#947;&#942;&#962; &#956;&#949; &#960;&#959;&#955;&#955;&#940; &#964;&#961;&#945;&#947;&#959;&#973;&#948;&#953;&#945;. &#917;&#954;&#964;&#965;&#960;&#974;&#957;&#949;&#953; &#945;&#963;&#960;&#961;&#972;&#956;&#945;&#965;&#961;&#945;.</themeExplanation>
<themeKind>insert</themeKind>
<themeSortOrder>8</themeSortOrder>
<themeMediaIndex>2</themeMediaIndex>
<insert>
</insert>
<cover type="frontInsert">
<element type="rect">
<fillColor>255, 255, 255</fillColor>
<penSize>0.0</penSize>
</element>
<element type="rect">
<contributesToHeader />
<noFill />
<noFrame />
<bottom kind="percentage">95.0</bottom>
</element>
<element type="rect">
<contributesToFooter />
<noFill />
<noFrame />
<top kind="percentage">97.0</top>
</element>
<element type="rect">
<contributesToLeftMargin />
<noFill />
<noFrame />
<right kind="percentage">97.0</right>
</element>
<element type="rect">
<contributesToRightMargin />
<noFill />
<noFrame />
<left kind="percentage">97.0</left>
</element>
<element type="playlist">
<left parent="content">0</left>
<top parent="content">5</top>
<right parent="content">0</right>
<bottom parent="content">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>7</textSize>
<smallestSize>7</smallestSize>
<maxColumns>2</maxColumns>
<textColor index="0">000, 000, 000</textColor>
<textFormat index="0">%TRACKNUMBER.</textFormat>
<textJustification index="0">left</textJustification>
<textColor index="1">000, 000, 000</textColor>
<textFormat index="1">%COMBOTRACKNAMEANDARTIST</textFormat>
<textJustification index="1">left</textJustification>
</element>
<element type="text">
<left parent="content">5</left>
<top parent="header">0</top>
<right parent="content">5</right>
<bottom kind="heightAbsoluteCenter" parent="header">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>12</textSize>
<textColor index="0">000, 000, 000</textColor>
<textFormat index="0">%COMBOPLAYLISTANDARTIST</textFormat>
<textJustification index="0">left</textJustification>
</element>
</cover>
<cover type="backInsert">
<element type="rect">
<fillColor>255, 255, 255</fillColor>
<penSize>0.0</penSize>
</element>
<element type="rect">
<contributesToHeader />
<noFill />
<noFrame />
<bottom kind="percentage">95.0</bottom>
</element>
<element type="rect">
<contributesToFooter />
<noFill />
<noFrame />
<top kind="percentage">97.0</top>
</element>
<element type="rect">
<contributesToLeftMargin />
<noFill />
<noFrame />
<right kind="percentage">97.0</right>
</element>
<element type="rect">
<contributesToRightMargin />
<noFill />
<noFrame />
<left kind="percentage">97.0</left>
</element>
<element type="text">
<left parent="footer">0</left>
<top parent="footer">0</top>
<right parent="footer">0</right>
<bottom kind="heightAbsoluteCenter" parent="footer">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>7</textSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%MADEWITHITUNES</textFormat>
<textJustification index="0">center</textJustification>
</element>
<element type="playlist">
<left parent="content">0</left>
<top parent="content">5</top>
<right parent="content">0</right>
<bottom parent="content">0</bottom>
<textSize>7</textSize>
<smallestSize>7</smallestSize>
<maxColumns>2</maxColumns>
<textColor index="0">000, 000, 000</textColor>
<textFormat index="0">%TRACKNUMBER.</textFormat>
<textJustification index="0">left</textJustification>
<textColor index="1">000, 000, 000</textColor>
<textFormat index="1">%COMBOTRACKNAMEANDARTIST</textFormat>
<textJustification index="1">left</textJustification>
<textJustification index="2">right</textJustification>
<overflowFormat>&#954;&#945;&#953; %TRACKNUMBER &#945;&#954;&#972;&#956;&#951;&#8230;</overflowFormat>
</element>
<element type="text">
<left parent="content">5</left>
<top parent="header">0</top>
<right parent="content">5</right>
<bottom kind="heightAbsoluteCenter" parent="header">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>12</textSize>
<textColor index="0">000, 000, 000</textColor>
<textFormat index="0">%COMBOPLAYLISTANDARTIST</textFormat>
<textJustification index="0">left</textJustification>
</element>
</cover>
</theme>
