<?xml version="1.0" encoding="UTF-8"?>
<theme>
<themeName>&#923;&#943;&#963;&#964;&#945; &#940;&#955;&#956;&#960;&#959;&#965;&#956;</themeName>
<themeExplanation>&#917;&#954;&#964;&#965;&#960;&#974;&#957;&#949;&#953; &#940;&#955;&#956;&#960;&#959;&#965;&#956; &#945;&#960;&#972; &#964;&#951;&#957; &#949;&#960;&#953;&#955;&#949;&#947;&#956;&#941;&#957;&#951; &#955;&#943;&#963;&#964;&#945; &#942; &#946;&#953;&#946;&#955;&#953;&#959;&#952;&#942;&#954;&#951;. &#916;&#949;&#957; &#949;&#954;&#964;&#965;&#960;&#974;&#957;&#949;&#953; &#964;&#943;&#964;&#955;&#959;&#965;&#962; &#964;&#961;&#945;&#947;&#959;&#965;&#948;&#953;&#974;&#957;.</themeExplanation>
<themeKind>albumlist</themeKind>
<themeInternalKind>playlist</themeInternalKind>
<themeSortOrder>2</themeSortOrder>
<themeMediaIndex>1</themeMediaIndex>
<albumlist>
<column>
<field>artistName</field>
<resizable />
<width>50</width>
</column>
<column>
<field>albumName</field>
<resizable />
<width>50</width>
</column>
<column>
<columnName>&#913;&#961;. &#963;&#964;&#959;&#953;&#967;&#949;&#943;&#969;&#957;</columnName>
<field>trackNumber</field>
<width>25</width>
</column>
<column>
<columnName>&#931;&#965;&#957;&#959;&#955;&#953;&#954;&#972;&#962; &#967;&#961;&#972;&#957;&#959;&#962;</columnName>
<field>totalTime</field>
<width>50</width>
</column>
</albumlist>
</theme>
