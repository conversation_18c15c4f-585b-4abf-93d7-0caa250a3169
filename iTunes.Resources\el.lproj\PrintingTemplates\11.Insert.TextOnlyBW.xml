<?xml version="1.0" encoding="UTF-8"?>
<theme>
<themeName>&#922;&#949;&#943;&#956;&#949;&#957;&#959; &#956;&#972;&#957;&#959; (&#913;&#963;&#960;&#961;&#972;&#956;&#945;&#965;&#961;&#945;)</themeName>
<themeExplanation>&#924;&#953;&#945; &#945;&#960;&#955;&#942; &#949;&#954;&#964;&#973;&#960;&#969;&#963;&#951; &#954;&#949;&#953;&#956;&#941;&#957;&#959;&#965; &#964;&#969;&#957; &#964;&#961;&#945;&#947;&#959;&#965;&#948;&#953;&#974;&#957; &#963;&#964;&#951;&#957; &#949;&#960;&#953;&#955;&#949;&#947;&#956;&#941;&#957;&#951; &#955;&#943;&#963;&#964;&#945; &#945;&#957;&#945;&#960;&#945;&#961;&#945;&#947;&#969;&#947;&#942;&#962; &#942; &#946;&#953;&#946;&#955;&#953;&#959;&#952;&#942;&#954;&#951;. &#917;&#954;&#964;&#965;&#960;&#974;&#957;&#949;&#953; &#945;&#963;&#960;&#961;&#972;&#956;&#945;&#965;&#961;&#945;.</themeExplanation>
<themeKind>insert</themeKind>
<themeSortOrder>5</themeSortOrder>
<themeMediaIndex>2</themeMediaIndex>
<insert>
<setDominantColor>255, 255, 255</setDominantColor>
</insert>
<cover type="frontInsert">
<element type="rect">
<fillColor>dominantColor</fillColor>
<penSize>0.0</penSize>
</element>
<element type="rect">
<contributesToFooter />
<noFill />
<noFrame />
<top kind="percentage">97.0</top>
</element>
<element type="text">
<contributesToFooter />
<left parent="content">10</left>
<top parent="content">0</top>
<right parent="content">10</right>
<bottom kind="heightAbsoluteBottom" parent="content">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>24</textSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%PLAYLISTNAME</textFormat>
<textJustification index="0">right</textJustification>
</element>
<element type="text">
<contributesToFooter />
<left parent="content">10</left>
<top parent="content">0</top>
<right parent="content">10</right>
<bottom kind="heightAbsoluteBottom" parent="content">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>24</textSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%ARTISTNAME</textFormat>
<textJustification index="0">right</textJustification>
</element>
</cover>
<cover type="backInsert">
<element type="rect">
<fillColor>dominantColor</fillColor>
<penSize>0.0</penSize>
</element>
<element type="rect">
<contributesToHeader />
<noFill />
<noFrame />
<bottom kind="percentage">90.0</bottom>
</element>
<element type="rect">
<contributesToFooter />
<noFill />
<noFrame />
<top kind="percentage">97.0</top>
</element>
<element type="rect">
<contributesToLeftMargin />
<noFill />
<noFrame />
<right kind="percentage">97.0</right>
</element>
<element type="rect">
<contributesToRightMargin />
<noFill />
<noFrame />
<left kind="percentage">97.0</left>
</element>
<element type="text">
<left parent="footer">0</left>
<top parent="footer">0</top>
<right parent="footer">0</right>
<bottom kind="heightAbsoluteCenter" parent="footer">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>7</textSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%MADEWITHITUNES</textFormat>
<textJustification index="0">center</textJustification>
</element>
<element type="playlist">
<left parent="content">5</left>
<top parent="content">5</top>
<right parent="content">5</right>
<bottom parent="content">5</bottom>
<textStyle>insertTracks</textStyle>
<textSize>12</textSize>
<smallestSize>9</smallestSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%TRACKNUMBER.</textFormat>
<textJustification index="0">left</textJustification>
<textColor index="1">dominantTextColor</textColor>
<textFormat index="1">%COMBOTRACKNAMEANDARTIST</textFormat>
<textJustification index="1">left</textJustification>
<textColor index="2">dominantTextColor</textColor>
<textFormat index="2">%TOTALTIME</textFormat>
<textJustification index="2">right</textJustification>
<overflowFormat>&#954;&#945;&#953; %TRACKNUMBER &#945;&#954;&#972;&#956;&#951;&#8230;</overflowFormat>
</element>
<element type="text">
<left parent="content">5</left>
<top parent="header">0</top>
<right parent="content">5</right>
<bottom kind="heightAbsoluteCenter" parent="header">0</bottom>
<textStyle>insertTracks</textStyle>
<textSize>14</textSize>
<textColor index="0">dominantTextColor</textColor>
<textFormat index="0">%COMBOPLAYLISTANDARTIST</textFormat>
<textJustification index="0">left</textJustification>
</element>
</cover>
</theme>
