<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AVAssetExportPresetICPL</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>640</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>880804</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>90</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1280</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>3355443</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>90</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLHEVC1920x1080</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1080</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>6000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLHEVC1280x1280WithHDR</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1280</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>4200000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2100_HLG</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>SMPTE_ST_2084_PQ</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLHEVC1920x1920WithHDR</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1920</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>6000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2100_HLG</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>SMPTE_ST_2084_PQ</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLAux</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>480</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>32</integer>
			<key>videoAverageBitRate</key>
			<integer>1150000</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>18</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetICPLAuxHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>720</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>32</integer>
			<key>videoAverageBitRate</key>
			<integer>4400000</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>18</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetPhotoStream</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>360</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>880804</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetPhotoStreamHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>720</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>2936013</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAVCHD</key>
	<dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC</string>
		</dict>
		<key>Video</key>
		<dict/>
	</dict>
	<key>AVAssetExportPresetAppleM4VCellular</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>300</integer>
			<key>maxWidth</key>
			<integer>400</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>10</integer>
			<key>maxFrameRate</key>
			<integer>10</integer>
			<key>videoAverageBitRate</key>
			<integer>225280</integer>
			<key>profileLevel</key>
			<string>H264_Baseline_1_2</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<false/>
			<key>usageMode</key>
			<integer>5</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AACHE_32kHz_Stereo_36kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4V480pSD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>480</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>3145728</integer>
			<key>dataRateLimitDuration</key>
			<real>2.59999990463257</real>
			<key>dataRateLimitBytes</key>
			<integer>1069600</integer>
			<key>profileLevel</key>
			<string>H264_Main_3_0</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>7</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4ViPod</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>480</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>1572864</integer>
			<key>dataRateLimitDuration</key>
			<real>2.59999990463257</real>
			<key>dataRateLimitBytes</key>
			<integer>534800</integer>
			<key>profileLevel</key>
			<string>H264_Baseline_3_0</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<false/>
			<key>usageMode</key>
			<integer>6</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4VAppleTV</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>540</integer>
			<key>maxWidth</key>
			<integer>960</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>5242880</integer>
			<key>dataRateLimitDuration</key>
			<real>1</real>
			<key>dataRateLimitBytes</key>
			<integer>1750000</integer>
			<key>profileLevel</key>
			<string>H264_Main_3_1</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>7</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4VAppleTVHD</key>
	<dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_256kbit</string>
		</dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>720</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>24</integer>
			<key>maxFrameRate</key>
			<integer>24</integer>
			<key>videoAverageBitRate</key>
			<integer>5242880</integer>
			<key>dataRateLimitDuration</key>
			<real>1</real>
			<key>dataRateLimitBytes</key>
			<integer>1750000</integer>
			<key>profileLevel</key>
			<string>H264_Main_3_1</string>
			<key>keyFrameInterval</key>
			<integer>24</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>7</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4VWiFi</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>360</integer>
			<key>maxWidth</key>
			<integer>480</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>921600</integer>
			<key>dataRateLimitDuration</key>
			<real>2.59999990463257</real>
			<key>dataRateLimitBytes</key>
			<integer>249600</integer>
			<key>profileLevel</key>
			<string>H264_Baseline_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<false/>
			<key>usageMode</key>
			<integer>8</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_128kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4V720pHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>720</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>7340032</integer>
			<key>dataRateLimitDuration</key>
			<integer>1</integer>
			<key>dataRateLimitBytes</key>
			<integer>2500000</integer>
			<key>profileLevel</key>
			<string>H264_Main_3_1</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>7</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_160kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4V1080pHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1080</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>30</integer>
			<key>videoAverageBitRate</key>
			<integer>10485760</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPreset640x480</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>640</integer>
			<key>maxWidth</key>
			<integer>640</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>3145728</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPreset960x540</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>960</integer>
			<key>maxWidth</key>
			<integer>960</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>5242880</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPreset1280x720</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>1280</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>7340032</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPreset1920x1080</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>1920</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>10485760</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVC1920x1080</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>1920</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>7500000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2100_HLG</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>SMPTE_ST_2084_PQ</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVC1920x1080WithAlpha</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>1920</integer>
			<key>maxWidth</key>
			<integer>1920</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>7500000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>alphaEncoding</key>
			<true/>
			<key>alphaQuality</key>
			<real>0.75</real>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPreset3840x2160</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>25000000</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVC3840x2160</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>15000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2100_HLG</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>SMPTE_ST_2084_PQ</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVC3840x2160WithAlpha</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>15000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>alphaEncoding</key>
			<true/>
			<key>alphaQuality</key>
			<real>0.75</real>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHighestQuality</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>25000000</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVCHighestQuality</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>15000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2100_HLG</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>SMPTE_ST_2084_PQ</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetHEVCHighestQualityWithAlpha</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>3840</integer>
			<key>maxWidth</key>
			<integer>3840</integer>
			<key>videoCodec</key>
			<integer>1752589105</integer>
			<key>videoAverageBitRate</key>
			<integer>15000000</integer>
			<key>profileLevel</key>
			<string>HEVC_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>alphaEncoding</key>
			<true/>
			<key>alphaQuality</key>
			<real>0.75</real>
			<key>colorPropertyTiers</key>
			<array>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_601_4</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>P3_D65</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
				<dict>
					<key>colorPrimaries</key>
					<string>ITU_R_2020</string>
					<key>transferFunction</key>
					<string>ITU_R_2020</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_2020</string>
				</dict>
			</array>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetMediumQuality</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>480</integer>
			<key>maxWidth</key>
			<integer>480</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>32</integer>
			<key>videoAverageBitRate</key>
			<integer>700000</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>maxAudioBitRate</key>
			<integer>168000</integer>
			<key>audioPreset</key>
			<string>Preset_AAC_44kHz_Mono_64kbit</string>
			<key>audioSettingPerChannelCount</key>
			<dict>
				<key>2</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Stereo_128kbit</string>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
				</dict>
			</dict>
		</dict>
	</dict>
	<key>AVAssetExportPresetMediumQuality_16x9</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>568</integer>
			<key>maxWidth</key>
			<integer>568</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>30</integer>
			<key>maxFrameRate</key>
			<integer>32</integer>
			<key>videoAverageBitRate</key>
			<integer>700000</integer>
			<key>profileLevel</key>
			<string>H264_Main_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>30</integer>
			<key>frameReordering</key>
			<true/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>maxAudioBitRate</key>
			<integer>168000</integer>
			<key>audioPreset</key>
			<string>Preset_AAC_44kHz_Mono_64kbit</string>
			<key>audioSettingPerChannelCount</key>
			<dict>
				<key>2</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Stereo_128kbit</string>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
				</dict>
			</dict>
		</dict>
	</dict>
	<key>AVAssetExportPresetLowQuality</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>192</integer>
			<key>maxWidth</key>
			<integer>192</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>15</integer>
			<key>maxFrameRate</key>
			<integer>16</integer>
			<key>videoAverageBitRate</key>
			<integer>128000</integer>
			<key>profileLevel</key>
			<string>H264_Baseline_AutoLevel</string>
			<key>keyFrameInterval</key>
			<integer>15</integer>
			<key>frameReordering</key>
			<false/>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AACHE_44kHz_Mono_24kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetLowQuality_16x9</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>maxHeight</key>
			<integer>224</integer>
			<key>maxWidth</key>
			<integer>224</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>frameDurationValue</key>
			<integer>1</integer>
			<key>frameDurationTimeScale</key>
			<integer>15</integer>
			<key>maxFrameRate</key>
			<integer>16</integer>
			<key>videoAverageBitRate</key>
			<integer>128000</integer>
			<key>profileLevel</key>
			<string>H264_Baseline_AutoLevel</string>
			<key>frameReordering</key>
			<false/>
			<key>keyFrameInterval</key>
			<integer>15</integer>
			<key>usageMode</key>
			<integer>0</integer>
			<key>colorPrimaries</key>
			<string>SMPTE_C</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_601_4</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AACHE_44kHz_Mono_24kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppPreview</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>ApplyRotation</string>
					<key>maxHeight</key>
					<integer>2208</integer>
					<key>maxWidth</key>
					<integer>2208</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>maxFrameRate</key>
					<integer>30</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>videoAverageBitRate</key>
					<integer>12582912</integer>
					<key>profileLevel</key>
					<string>H264_High_AutoLevel</string>
					<key>entropyMode</key>
					<string>CABAC</string>
					<key>keyFrameInterval</key>
					<integer>30</integer>
					<key>frameReordering</key>
					<true/>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_LPCM</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>ApplyRotation</string>
					<key>maxHeight</key>
					<integer>1334</integer>
					<key>maxWidth</key>
					<integer>1334</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>maxFrameRate</key>
					<integer>30</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>videoAverageBitRate</key>
					<integer>9437184</integer>
					<key>profileLevel</key>
					<string>H264_High_AutoLevel</string>
					<key>entropyMode</key>
					<string>CABAC</string>
					<key>keyFrameInterval</key>
					<integer>30</integer>
					<key>frameReordering</key>
					<true/>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_LPCM</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetAppleProRes422LPCM</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>videoCodec</key>
			<integer>1634755438</integer>
			<key>usageMode</key>
			<integer>0</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_LPCM</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleProRes4444LPCM</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>ApplyRotation</string>
			<key>videoCodec</key>
			<integer>1634743400</integer>
			<key>usageMode</key>
			<integer>0</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_LPCM</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetAppleM4A</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoCodec</key>
			<integer>1851876449</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_48kHz_Stereo_256kbit</string>
		</dict>
	</dict>
	<key>AVAssetExportPresetMessage</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>960</integer>
					<key>maxWidth</key>
					<integer>960</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>1250000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>640</integer>
					<key>maxWidth</key>
					<integer>640</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>900000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>480</integer>
					<key>maxWidth</key>
					<integer>480</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>700000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>320</integer>
					<key>maxWidth</key>
					<integer>320</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>420000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>192</integer>
					<key>maxWidth</key>
					<integer>192</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>128000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AACHE_44kHz_Mono_24kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetMessage_16x9</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>1280</integer>
					<key>maxWidth</key>
					<integer>1280</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>2200000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>960</integer>
					<key>maxWidth</key>
					<integer>960</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>1250000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>568</integer>
					<key>maxWidth</key>
					<integer>568</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>700000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>352</integer>
					<key>maxWidth</key>
					<integer>352</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>420000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>224</integer>
					<key>maxWidth</key>
					<integer>224</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>128000</integer>
					<key>profileLevel</key>
					<string>H264_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AACHE_44kHz_Mono_24kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetMessageHEVCWithHDR</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>960</integer>
					<key>maxWidth</key>
					<integer>960</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>1250000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>640</integer>
					<key>maxWidth</key>
					<integer>640</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>900000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>480</integer>
					<key>maxWidth</key>
					<integer>480</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>700000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>320</integer>
					<key>maxWidth</key>
					<integer>320</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>420000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>192</integer>
					<key>maxWidth</key>
					<integer>192</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>128000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AACHE_44kHz_Mono_24kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetMessageHEVCWithHDR_16x9</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>1280</integer>
					<key>maxWidth</key>
					<integer>1280</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>2200000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>960</integer>
					<key>maxWidth</key>
					<integer>960</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>1250000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>568</integer>
					<key>maxWidth</key>
					<integer>568</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>videoAverageBitRate</key>
					<integer>700000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>352</integer>
					<key>maxWidth</key>
					<integer>352</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>420000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>224</integer>
					<key>maxWidth</key>
					<integer>224</integer>
					<key>videoCodec</key>
					<integer>1752589105</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>15</integer>
					<key>maxFrameRate</key>
					<integer>16</integer>
					<key>videoAverageBitRate</key>
					<integer>128000</integer>
					<key>profileLevel</key>
					<string>HEVC_Main_AutoLevel</string>
					<key>usageMode</key>
					<integer>0</integer>
					<key>colorPropertyTiers</key>
					<array>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_709_2</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>P3_D65</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_709_2</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_709_2</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2020</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>ITU_R_2100_HLG</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
						<dict>
							<key>colorPrimaries</key>
							<string>ITU_R_2020</string>
							<key>transferFunction</key>
							<string>SMPTE_ST_2084_PQ</string>
							<key>ycbcrMatrix</key>
							<string>ITU_R_2020</string>
						</dict>
					</array>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AACHE_44kHz_Mono_24kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetMessageAudio</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoCodec</key>
					<integer>1851876449</integer>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoCodec</key>
					<integer>1851876449</integer>
				</dict>
				<key>Audio</key>
				<dict>
					<key>audioPreset</key>
					<string>Preset_AAC_44kHz_Mono_64kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetMessageAux</key>
	<dict>
		<key>mediaTiers</key>
		<array>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>720</integer>
					<key>maxWidth</key>
					<integer>960</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>keyFrameInterval</key>
					<integer>18</integer>
					<key>videoAverageBitRate</key>
					<integer>4400000</integer>
					<key>profileLevel</key>
					<string>H264_High_AutoLevel</string>
					<key>entropyMode</key>
					<string>CABAC</string>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>540</integer>
					<key>maxWidth</key>
					<integer>720</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>keyFrameInterval</key>
					<integer>18</integer>
					<key>videoAverageBitRate</key>
					<integer>2875000</integer>
					<key>profileLevel</key>
					<string>H264_High_AutoLevel</string>
					<key>entropyMode</key>
					<string>CABAC</string>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
			<dict>
				<key>Video</key>
				<dict>
					<key>videoMatrixHandling</key>
					<string>PreserveMatrix</string>
					<key>maxHeight</key>
					<integer>480</integer>
					<key>maxWidth</key>
					<integer>640</integer>
					<key>videoCodec</key>
					<integer>1635148593</integer>
					<key>frameDurationValue</key>
					<integer>1</integer>
					<key>frameDurationTimeScale</key>
					<integer>30</integer>
					<key>maxFrameRate</key>
					<integer>32</integer>
					<key>keyFrameInterval</key>
					<integer>18</integer>
					<key>videoAverageBitRate</key>
					<integer>1150000</integer>
					<key>profileLevel</key>
					<string>H264_High_AutoLevel</string>
					<key>entropyMode</key>
					<string>CABAC</string>
					<key>colorPrimaries</key>
					<string>ITU_R_709_2</string>
					<key>transferFunction</key>
					<string>ITU_R_709_2</string>
					<key>ycbcrMatrix</key>
					<string>ITU_R_709_2</string>
				</dict>
				<key>Audio</key>
				<dict>
					<key>maxAudioBitRate</key>
					<integer>168000</integer>
					<key>audioPreset</key>
					<string>Preset_AAC_48kHz_Stereo_128kbit</string>
				</dict>
			</dict>
		</array>
	</dict>
	<key>AVAssetExportPresetYouTubeHD</key>
	<dict>
		<key>Video</key>
		<dict>
			<key>videoMatrixHandling</key>
			<string>PreserveMatrix</string>
			<key>maxHeight</key>
			<integer>1280</integer>
			<key>maxWidth</key>
			<integer>1280</integer>
			<key>videoCodec</key>
			<integer>1635148593</integer>
			<key>videoAverageBitRate</key>
			<integer>4500000</integer>
			<key>profileLevel</key>
			<string>H264_High_AutoLevel</string>
			<key>entropyMode</key>
			<string>CABAC</string>
			<key>colorPrimaries</key>
			<string>ITU_R_709_2</string>
			<key>transferFunction</key>
			<string>ITU_R_709_2</string>
			<key>ycbcrMatrix</key>
			<string>ITU_R_709_2</string>
		</dict>
		<key>Audio</key>
		<dict>
			<key>audioPreset</key>
			<string>Preset_AAC_44kHz_Stereo_256kbit</string>
		</dict>
	</dict>
</dict>
</plist>
