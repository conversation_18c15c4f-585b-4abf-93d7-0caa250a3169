<?xml version="1.0" encoding="UTF-8"?>
<theme>
	<themeName>Liste over album</themeName>
	<themeExplanation>Udskriver album fra den valgte spilleliste eller det valgte bibliotek, sorteret efter kunstner. Udskriver ikke sangtitler.</themeExplanation>
	<themeKind>albumlist</themeKind>
	<themeInternalKind>playlist</themeInternalKind>
	<themeSortOrder>2</themeSortOrder>
	<themeMediaIndex>1</themeMediaIndex>
	<albumlist>
		<column>
			<field>artistName</field>
			<resizable />
			<width>50</width>
		</column>
		<column>
			<field>albumName</field>
			<resizable />
			<width>50</width>
		</column>
		<column>
			<columnName># Items</columnName>
			<field>trackNumber</field>
			<width>25</width>
		</column>
		<column>
			<columnName>Total Time</columnName>
			<field>totalTime</field>
			<width>50</width>
		</column>
	</albumlist>
</theme>
