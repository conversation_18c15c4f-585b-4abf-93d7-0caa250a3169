{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "CheatManager", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "BlurPlane", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "FakeBat", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "ConfigManager", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "CoreLoop", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "Platform", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "Grass", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "TransitionPoint", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "GCManager", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}]}