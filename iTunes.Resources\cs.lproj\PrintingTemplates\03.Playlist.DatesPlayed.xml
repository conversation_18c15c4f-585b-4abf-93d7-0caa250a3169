<?xml version="1.0" encoding="UTF-8"?>
<theme>
<themeName>Data p&#345;ehr&#225;n&#237;</themeName>
<themeExplanation>Vytiskne n&#225;zev, d&#233;lku, interpreta, album, po&#269;et p&#345;ehr&#225;n&#237; a datum pro skladby ve vybran&#233;m seznamu stop nebo knihovn&#283;. Tiskne v orientaci na &#353;&#237;&#345;ku.</themeExplanation>
<themeKind>playlist</themeKind>
<themeSortOrder>3</themeSortOrder>
<themeMediaIndex>2</themeMediaIndex>
<playlist>
		<column>
			<trackIndex />
			<width>10</width>
		</column>
<column>
<field>trackName</field>
<resizable />
<width>50</width>
</column>
<column>
<field>totalTime</field>
<width>40</width>
</column>
<column>
<field>albumName</field>
<resizable />
<width>50</width>
</column>
<column>
<field>artistName</field>
<resizable />
<width>50</width>
</column>
<column>
<field>playDate</field>
<resizable />
<width>30</width>
</column>
<column>
<field>playCount</field>
<width>50</width>
</column>
</playlist>
</theme>
