<html xmlns="http://www.w3.org/100/xhtml" lang="en-US">
<head>
<meta charset="UTF-8" />
<meta http=equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>iTunes-ohje</title> 
	
<style type="text/css">
body { background-color: #fff; font-family: "Lucida Grande", Helvetica, Arial, sans-serif; font-size: 1em; line-height: 1.5em; min-width: 350px; padding: 0; margin: 0; overflow: hidden; }
.contentNotAvailableMessage div.content#mainContent,
.contentNotAvailableMessage { font-size: .75em; line-height: 1.5; color: #333; width: 448px; display: table; margin: 0 auto; padding: 28px; }
.contentNotAvailableMessage .Subhead { border: 1px solid #eaeaea; -webkit-border-radius: 6px; border-radius: 6px; padding: 30px; -webkit-box-shadow: 0em 0.1em 0.25em #cccccc; }
.contentNotAvailableMessage .Subhead .Name { margin-top: 0 !important; font-size: 14px; line-height: 18px; font-weight: bold; }
.contentNotAvailableMessage .Subhead .Name + .Para { font-size: 12px; line-height: 18px; font-weight: normal; margin-top: -5px !important; margin-bottom: 0 !important; padding: 0; }
</style>

</head>

<body><a name="win-download"></a>
<div id="mainContentWrapper" class="contentNotAvailableMessage">
	<div class="content" id="mainContent">
		<div class="Subhead">
			<p class="Name">
				iTunes-ohje ei ole saatavilla, koska tietokone ei ole yhteydessä internetiin.
			</p>
			<p class="Para">
				Varmista, että tietokone on yhteydessä internetiin katsoaksesi iTunes-ohjetta. Valitse sitten iTunes-ohje Ohje-valikosta.
			</p>
		</div>
	</div>
</div>
</div>
</body>
</html>
